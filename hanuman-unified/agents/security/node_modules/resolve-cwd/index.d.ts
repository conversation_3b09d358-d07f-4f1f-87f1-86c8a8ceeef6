declare const resolveCwd: {
	/**
	Resolve the path of a module like [`require.resolve()`](https://nodejs.org/api/globals.html#globals_require_resolve) but from the current working directory.

	@param moduleId - What you would use in `require()`.
	@returns The resolved module path.
	@throws When the module can't be found.

	@example
	```
	import resolveCwd = require('resolve-cwd');

	console.log(__dirname);
	//=> '/Users/<USER>/rainbow'

	console.log(process.cwd());
	//=> '/Users/<USER>/unicorn'

	console.log(resolveCwd('./foo'));
	//=> '/Users/<USER>/unicorn/foo.js'
	```
	*/
	(moduleId: string): string;

	/**
	Resolve the path of a module like [`require.resolve()`](https://nodejs.org/api/globals.html#globals_require_resolve) but from the current working directory.

	@param moduleId - What you would use in `require()`.
	@returns The resolved module path. Returns `undefined` instead of throwing when the module can't be found.

	@example
	```
	import resolveCwd = require('resolve-cwd');

	console.log(__dirname);
	//=> '/Users/<USER>/rainbow'

	console.log(process.cwd());
	//=> '/Users/<USER>/unicorn'

	console.log(resolveCwd.silent('./foo'));
	//=> '/Users/<USER>/unicorn/foo.js'
	```
	*/
	silent(moduleId: string): string | undefined;
};

export = resolveCwd;
